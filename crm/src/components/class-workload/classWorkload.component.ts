import { IComponentOptions, IController } from "angular";
import { Class } from "../../core/class";

class ClassWorkloadController implements IController {
  targetClass: Class;

  /** @ngInject */
  constructor() { }

  $onInit() { }
}

export const ClassWorkloadComponent: IComponentOptions = {
  template: require('./classWorkload.component.html'),
  controller: ClassWorkloadController,
  bindings: {
    targetClass: '=',
  },
}